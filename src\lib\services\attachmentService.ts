import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage, auth } from '@/lib/firebase';
import { AttachmentMetadata } from '@/lib/types/chat';

export interface UploadAttachmentParams {
  file: File;
  username: string;
  chatId: string;
}

export interface ProcessedAttachment {
  metadata: AttachmentMetadata;
  base64Data?: string; // Para PDFs
}

class AttachmentService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly SUPPORTED_IMAGE_TYPES = ['image/png', 'image/jpeg', 'image/webp'];
  private readonly SUPPORTED_PDF_TYPE = 'application/pdf';

  /**
   * Valida se o arquivo é suportado
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Verificar tamanho
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: 'Arquivo muito grande. Máximo permitido: 10MB'
      };
    }

    // Verificar tipo
    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);
    const isPdf = file.type === this.SUPPORTED_PDF_TYPE;

    if (!isImage && !isPdf) {
      return {
        isValid: false,
        error: 'Tipo de arquivo não suportado. Use PNG, JPEG, WebP ou PDF'
      };
    }

    return { isValid: true };
  }

  /**
   * Converte arquivo para base64
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remover o prefixo data:type;base64,
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Gera ID único para anexo
   */
  private generateAttachmentId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * Faz upload do arquivo para Firebase Storage
   */
  async uploadAttachment(params: UploadAttachmentParams): Promise<ProcessedAttachment> {
    const { file, username, chatId } = params;

    console.log(`🔄 Iniciando upload do arquivo: ${file.name}`);
    console.log(`📊 Tamanho: ${file.size} bytes, Tipo: ${file.type}`);
    console.log(`👤 Username: ${username}, 💬 ChatId: ${chatId}`);

    // Verificar autenticação
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log('❌ Usuário não autenticado');
      throw new Error('Usuário não autenticado. Faça login novamente.');
    }
    console.log(`🔐 Usuário autenticado: ${currentUser.uid}`);

    // Validar arquivo
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      console.log(`❌ Validação falhou: ${validation.error}`);
      throw new Error(validation.error);
    }
    console.log('✅ Arquivo validado com sucesso');

    try {
      // Gerar ID único para o anexo
      const attachmentId = this.generateAttachmentId();
      console.log(`🆔 ID do anexo gerado: ${attachmentId}`);

      // Determinar tipo
      const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);
      const type: 'image' | 'pdf' = isImage ? 'image' : 'pdf';
      console.log(`📄 Tipo determinado: ${type}`);

      // Criar caminho no Storage
      const storagePath = `usuarios/${username}/conversas/${chatId}/anexos/${attachmentId}_${file.name}`;
      console.log(`📁 Caminho no Storage: ${storagePath}`);
      const storageRef = ref(storage, storagePath);

      // Upload do arquivo
      console.log('🚀 Iniciando upload para Firebase Storage...');
      await uploadBytes(storageRef, file);
      console.log('✅ Upload para Firebase Storage concluído');

      // Obter URL de download
      console.log('🔗 Obtendo URL de download...');
      const downloadURL = await getDownloadURL(storageRef);
      console.log(`✅ URL de download obtida: ${downloadURL.substring(0, 100)}...`);

      // Preparar metadados
      const metadata: AttachmentMetadata = {
        id: attachmentId,
        type,
        filename: file.name,
        url: downloadURL,
        size: file.size,
        uploadedAt: Date.now(),
        storagePath,
        isActive: true // Por padrão, anexos são ativos
      };
      console.log('📋 Metadados preparados:', metadata);

      // Para PDFs, também converter para base64 para envio ao OpenRouter
      let base64Data: string | undefined;
      if (type === 'pdf') {
        console.log('📄 Convertendo PDF para base64...');
        base64Data = await this.fileToBase64(file);
        metadata.base64Data = base64Data;
        console.log(`✅ PDF convertido para base64 (${base64Data.length} caracteres)`);
      }

      console.log(`🎉 Upload do arquivo ${file.name} concluído com sucesso!`);
      return {
        metadata,
        base64Data
      };

    } catch (error) {
      console.error(`❌ Erro ao fazer upload do anexo ${file.name}:`, error);
      throw new Error(`Falha no upload do arquivo ${file.name}. Tente novamente.`);
    }
  }

  /**
   * Processa múltiplos arquivos
   */
  async uploadMultipleAttachments(
    files: File[],
    username: string,
    chatId: string
  ): Promise<ProcessedAttachment[]> {
    console.log(`📦 Iniciando upload de ${files.length} arquivo(s)`);
    const results: ProcessedAttachment[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`📁 Processando arquivo ${i + 1}/${files.length}: ${file.name}`);

      try {
        const result = await this.uploadAttachment({ file, username, chatId });
        results.push(result);
        console.log(`✅ Arquivo ${i + 1}/${files.length} processado com sucesso`);
      } catch (error) {
        console.error(`❌ Erro ao processar arquivo ${file.name}:`, error);
        // Continuar com os outros arquivos
      }
    }

    console.log(`🎯 Upload múltiplo concluído: ${results.length}/${files.length} arquivos processados`);
    return results;
  }

  /**
   * Prepara anexos para envio ao OpenRouter
   */
  prepareAttachmentsForOpenRouter(attachments: AttachmentMetadata[]): unknown[] {
    const openRouterContent: unknown[] = [];

    for (const attachment of attachments) {
      if (attachment.type === 'image') {
        // Para imagens, usar URL
        openRouterContent.push({
          type: 'image_url',
          image_url: {
            url: attachment.url
          }
        });
      } else if (attachment.type === 'pdf' && attachment.base64Data) {
        // Para PDFs, usar base64 com formato file
        const dataUrl = `data:application/pdf;base64,${attachment.base64Data}`;
        openRouterContent.push({
          type: 'file',
          file: {
            filename: attachment.filename,
            file_data: dataUrl
          }
        });
      }
    }

    return openRouterContent;
  }

  /**
   * Prepara plugins para PDFs (engine mistral-ocr)
   */
  preparePDFPlugins(attachments: AttachmentMetadata[]): unknown[] {
    const hasPDF = attachments.some(att => att.type === 'pdf');

    if (!hasPDF) {
      return [];
    }

    return [
      {
        id: 'file-parser',
        pdf: {
          engine: 'mistral-ocr'
        }
      }
    ];
  }

  /**
   * Limpa anexos temporários (se necessário)
   */
  async cleanupTemporaryAttachments(attachments: AttachmentMetadata[]): Promise<void> {
    // Implementar limpeza se necessário
    // Por enquanto, mantemos os arquivos no Storage
    console.log('Cleanup de anexos temporários:', attachments.length);
  }
}

// Exportar instância singleton
export const attachmentService = new AttachmentService();
export default attachmentService;
