import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';
import { AttachmentMetadata } from '@/lib/types/chat';

export interface UploadAttachmentParams {
  file: File;
  username: string;
  chatId: string;
}

export interface ProcessedAttachment {
  metadata: AttachmentMetadata;
  base64Data?: string; // Para PDFs
}

class AttachmentService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly SUPPORTED_IMAGE_TYPES = ['image/png', 'image/jpeg', 'image/webp'];
  private readonly SUPPORTED_PDF_TYPE = 'application/pdf';

  /**
   * Valida se o arquivo é suportado
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    console.log('=== DEBUG: VALIDANDO ARQUIVO ===');
    console.log('Nome:', file.name);
    console.log('Tipo:', file.type);
    console.log('Tamanho:', file.size);
    console.log('Tamanho máximo permitido:', this.MAX_FILE_SIZE);

    // Verificar tamanho
    if (file.size > this.MAX_FILE_SIZE) {
      console.log('=== DEBUG: ARQUIVO MUITO GRANDE ===');
      return {
        isValid: false,
        error: 'Arquivo muito grande. Máximo permitido: 10MB'
      };
    }

    // Verificar tipo
    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);
    const isPdf = file.type === this.SUPPORTED_PDF_TYPE;

    console.log('=== DEBUG: VERIFICAÇÃO DE TIPO ===');
    console.log('É imagem:', isImage);
    console.log('É PDF:', isPdf);
    console.log('Tipos de imagem suportados:', this.SUPPORTED_IMAGE_TYPES);
    console.log('Tipo PDF suportado:', this.SUPPORTED_PDF_TYPE);

    if (!isImage && !isPdf) {
      console.log('=== DEBUG: TIPO DE ARQUIVO NÃO SUPORTADO ===');
      return {
        isValid: false,
        error: 'Tipo de arquivo não suportado. Use PNG, JPEG, WebP ou PDF'
      };
    }

    console.log('=== DEBUG: ARQUIVO VÁLIDO ===');
    return { isValid: true };
  }

  /**
   * Converte arquivo para base64
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remover o prefixo data:type;base64,
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Gera ID único para anexo
   */
  private generateAttachmentId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * Faz upload do arquivo para Firebase Storage
   */
  async uploadAttachment(params: UploadAttachmentParams): Promise<ProcessedAttachment> {
    const { file, username, chatId } = params;

    console.log('=== DEBUG: ATTACHMENT SERVICE - UPLOAD INICIADO ===');
    console.log('Arquivo:', file.name);
    console.log('Username:', username);
    console.log('ChatId:', chatId);

    // Validar arquivo
    const validation = this.validateFile(file);
    console.log('=== DEBUG: VALIDAÇÃO DO ARQUIVO ===');
    console.log('Validação:', validation);

    if (!validation.isValid) {
      console.error('=== DEBUG: ARQUIVO INVÁLIDO ===');
      console.error('Erro:', validation.error);
      throw new Error(validation.error);
    }

    try {
      // Gerar ID único para o anexo
      const attachmentId = this.generateAttachmentId();
      console.log('=== DEBUG: ID DO ANEXO GERADO ===');
      console.log('AttachmentId:', attachmentId);

      // Determinar tipo
      const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);
      const type: 'image' | 'pdf' = isImage ? 'image' : 'pdf';
      console.log('=== DEBUG: TIPO DO ARQUIVO ===');
      console.log('Tipo determinado:', type);
      console.log('É imagem:', isImage);

      // Criar caminho no Storage
      const storagePath = `usuarios/${username}/conversas/${chatId}/anexos/${attachmentId}_${file.name}`;
      console.log('=== DEBUG: CAMINHO NO STORAGE ===');
      console.log('Storage path:', storagePath);

      const storageRef = ref(storage, storagePath);
      console.log('=== DEBUG: REFERÊNCIA DO STORAGE CRIADA ===');

      // Upload do arquivo
      console.log('=== DEBUG: INICIANDO UPLOAD PARA FIREBASE STORAGE ===');
      await uploadBytes(storageRef, file);
      console.log('=== DEBUG: UPLOAD PARA FIREBASE STORAGE CONCLUÍDO ===');

      // Obter URL de download
      console.log('=== DEBUG: OBTENDO URL DE DOWNLOAD ===');
      const downloadURL = await getDownloadURL(storageRef);
      console.log('=== DEBUG: URL DE DOWNLOAD OBTIDA ===');
      console.log('Download URL:', downloadURL);

      // Preparar metadados
      const metadata: AttachmentMetadata = {
        id: attachmentId,
        type,
        filename: file.name,
        url: downloadURL,
        size: file.size,
        uploadedAt: Date.now(),
        storagePath,
        isActive: true // Por padrão, anexos são ativos
      };

      console.log('=== DEBUG: METADADOS PREPARADOS ===');
      console.log('Metadata:', metadata);

      // Para PDFs, também converter para base64 para envio ao OpenRouter
      let base64Data: string | undefined;
      if (type === 'pdf') {
        console.log('=== DEBUG: CONVERTENDO PDF PARA BASE64 ===');
        base64Data = await this.fileToBase64(file);
        metadata.base64Data = base64Data;
        console.log('=== DEBUG: PDF CONVERTIDO PARA BASE64 ===');
        console.log('Base64 length:', base64Data?.length);
      }

      const result = {
        metadata,
        base64Data
      };

      console.log('=== DEBUG: UPLOAD ATTACHMENT CONCLUÍDO COM SUCESSO ===');
      console.log('Resultado:', result);
      return result;

    } catch (error) {
      console.error('=== DEBUG: ERRO NO UPLOAD ATTACHMENT ===');
      console.error('Erro:', error);
      throw new Error('Falha no upload do arquivo. Tente novamente.');
    }
  }

  /**
   * Processa múltiplos arquivos
   */
  async uploadMultipleAttachments(
    files: File[],
    username: string,
    chatId: string
  ): Promise<ProcessedAttachment[]> {
    console.log('=== DEBUG: UPLOAD MÚLTIPLOS ANEXOS INICIADO ===');
    console.log('Número de arquivos:', files.length);
    console.log('Username:', username);
    console.log('ChatId:', chatId);

    const results: ProcessedAttachment[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`=== DEBUG: PROCESSANDO ARQUIVO ${i + 1}/${files.length} ===`);
      console.log('Nome do arquivo:', file.name);

      try {
        const result = await this.uploadAttachment({ file, username, chatId });
        results.push(result);
        console.log(`=== DEBUG: ARQUIVO ${i + 1} PROCESSADO COM SUCESSO ===`);
      } catch (error) {
        console.error(`=== DEBUG: ERRO AO PROCESSAR ARQUIVO ${file.name} ===`);
        console.error('Erro:', error);
        // Continuar com os outros arquivos
      }
    }

    console.log('=== DEBUG: UPLOAD MÚLTIPLOS ANEXOS CONCLUÍDO ===');
    console.log('Resultados:', results.length, 'de', files.length, 'arquivos processados');
    return results;
  }

  /**
   * Prepara anexos para envio ao OpenRouter
   */
  prepareAttachmentsForOpenRouter(attachments: AttachmentMetadata[]): unknown[] {
    const openRouterContent: unknown[] = [];

    for (const attachment of attachments) {
      if (attachment.type === 'image') {
        // Para imagens, usar URL
        openRouterContent.push({
          type: 'image_url',
          image_url: {
            url: attachment.url
          }
        });
      } else if (attachment.type === 'pdf' && attachment.base64Data) {
        // Para PDFs, usar base64 com formato file
        const dataUrl = `data:application/pdf;base64,${attachment.base64Data}`;
        openRouterContent.push({
          type: 'file',
          file: {
            filename: attachment.filename,
            file_data: dataUrl
          }
        });
      }
    }

    return openRouterContent;
  }

  /**
   * Prepara plugins para PDFs (engine mistral-ocr)
   */
  preparePDFPlugins(attachments: AttachmentMetadata[]): unknown[] {
    const hasPDF = attachments.some(att => att.type === 'pdf');

    if (!hasPDF) {
      return [];
    }

    return [
      {
        id: 'file-parser',
        pdf: {
          engine: 'mistral-ocr'
        }
      }
    ];
  }

  /**
   * Limpa anexos temporários (se necessário)
   */
  async cleanupTemporaryAttachments(attachments: AttachmentMetadata[]): Promise<void> {
    // Implementar limpeza se necessário
    // Por enquanto, mantemos os arquivos no Storage
    console.log('Cleanup de anexos temporários:', attachments.length);
  }
}

// Exportar instância singleton
export const attachmentService = new AttachmentService();
export default attachmentService;
